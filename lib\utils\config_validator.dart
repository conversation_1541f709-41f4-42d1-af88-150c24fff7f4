import 'dart:io';
import 'package:path/path.dart' as path;
import '../models/vaultwarden_config.dart';
import '../services/logging_service.dart';

/// Configuration validation utilities
class ConfigValidator {
  /// Validate a complete Vaultwarden configuration
  static ValidationResult validateConfig(VaultwardenConfig config) {
    final errors = <String>[];
    final warnings = <String>[];

    // Validate port
    final portValidation = validatePort(config.port);
    if (!portValidation.isValid) {
      errors.addAll(portValidation.errors);
    }
    warnings.addAll(portValidation.warnings);

    // Validate data path
    final pathValidation = validateDataPath(config.dataPath);
    if (!pathValidation.isValid) {
      errors.addAll(pathValidation.errors);
    }
    warnings.addAll(pathValidation.warnings);

    // Validate security settings
    final securityValidation = validateSecuritySettings(config);
    warnings.addAll(securityValidation.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate port number
  static ValidationResult validatePort(int port) {
    final errors = <String>[];
    final warnings = <String>[];

    if (port < 1 || port > 65535) {
      errors.add('Port must be between 1 and 65535');
    } else if (port < 1024) {
      warnings.add('Port $port requires administrator privileges on most systems');
    } else if (_isCommonlyUsedPort(port)) {
      warnings.add('Port $port is commonly used by other services');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate data path
  static ValidationResult validateDataPath(String dataPath) {
    final errors = <String>[];
    final warnings = <String>[];

    if (dataPath.isEmpty) {
      errors.add('Data path cannot be empty');
      return ValidationResult(isValid: false, errors: errors, warnings: warnings);
    }

    try {
      final directory = Directory(dataPath);
      
      // Check if path is absolute
      if (!path.isAbsolute(dataPath)) {
        warnings.add('Relative paths may cause issues. Consider using absolute paths.');
      }

      // Check if directory exists
      if (!directory.existsSync()) {
        try {
          directory.createSync(recursive: true);
          LoggingService.config('Created data directory: $dataPath');
        } catch (e) {
          errors.add('Cannot create data directory: $dataPath. Error: $e');
        }
      }

      // Check permissions
      if (directory.existsSync()) {
        try {
          // Test write permissions by creating a temporary file
          final testFile = File(path.join(dataPath, '.write_test'));
          testFile.writeAsStringSync('test');
          testFile.deleteSync();
        } catch (e) {
          errors.add('No write permission for data directory: $dataPath');
        }
      }

      // Check available space (warning only)
      if (directory.existsSync()) {
        try {
          directory.statSync(); // Just verify we can access the directory
          // This is a simplified check - in production you'd want to check actual disk space
          LoggingService.debug('Data directory accessible: $dataPath');
        } catch (e) {
          warnings.add('Could not verify disk space for: $dataPath');
        }
      }

    } catch (e) {
      errors.add('Invalid data path: $dataPath. Error: $e');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate security settings
  static ValidationResult validateSecuritySettings(VaultwardenConfig config) {
    final warnings = <String>[];

    if (config.signupsAllowed) {
      warnings.add('Signups are enabled. Consider disabling after creating your account.');
    }

    if (!config.localhostOnly) {
      warnings.add('Network access is enabled for LAN. Ensure your network is secure.');
    }

    return ValidationResult(
      isValid: true,
      errors: [],
      warnings: warnings,
    );
  }

  /// Check if port is commonly used by other services
  static bool _isCommonlyUsedPort(int port) {
    const commonPorts = {
      22: 'SSH',
      23: 'Telnet',
      25: 'SMTP',
      53: 'DNS',
      80: 'HTTP',
      110: 'POP3',
      143: 'IMAP',
      443: 'HTTPS',
      993: 'IMAPS',
      995: 'POP3S',
      3389: 'RDP',
      5432: 'PostgreSQL',
      3306: 'MySQL',
      6379: 'Redis',
      27017: 'MongoDB',
    };

    return commonPorts.containsKey(port);
  }

  /// Get default configuration with validation
  static VaultwardenConfig getDefaultConfig() {
    final defaultDataPath = _getDefaultDataPath();
    
    return VaultwardenConfig(
      signupsAllowed: false, // Secure by default
      dataPath: defaultDataPath,
      port: 11001, // Non-standard port to avoid conflicts
      localhostOnly: true, // Secure by default
    );
  }

  /// Get platform-appropriate default data path
  static String _getDefaultDataPath() {
    if (Platform.isWindows) {
      final appData = Platform.environment['APPDATA'];
      if (appData != null) {
        return path.join(appData, 'VaAulLT', 'vaultwarden-data');
      }
      return path.join(Platform.environment['USERPROFILE'] ?? 'C:\\', 'VaAulLT', 'vaultwarden-data');
    } else if (Platform.isMacOS) {
      final home = Platform.environment['HOME'];
      return path.join(home ?? '/tmp', 'Library', 'Application Support', 'VaAulLT', 'vaultwarden-data');
    } else {
      // Linux and others
      final home = Platform.environment['HOME'];
      return path.join(home ?? '/tmp', '.local', 'share', 'VaAulLT', 'vaultwarden-data');
    }
  }

  /// Sanitize configuration values
  static VaultwardenConfig sanitizeConfig(VaultwardenConfig config) {
    return VaultwardenConfig(
      signupsAllowed: config.signupsAllowed,
      dataPath: path.normalize(config.dataPath.trim()),
      port: config.port.clamp(1, 65535),
      localhostOnly: config.localhostOnly,
      isRunning: config.isRunning,
    );
  }
}

/// Result of configuration validation
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  /// Check if there are any issues (errors or warnings)
  bool get hasIssues => errors.isNotEmpty || warnings.isNotEmpty;

  /// Get formatted message for display
  String get formattedMessage {
    final buffer = StringBuffer();
    
    if (errors.isNotEmpty) {
      buffer.writeln('Errors:');
      for (final error in errors) {
        buffer.writeln('  • $error');
      }
    }
    
    if (warnings.isNotEmpty) {
      if (buffer.isNotEmpty) buffer.writeln();
      buffer.writeln('Warnings:');
      for (final warning in warnings) {
        buffer.writeln('  • $warning');
      }
    }
    
    return buffer.toString().trim();
  }

  @override
  String toString() => 'ValidationResult(isValid: $isValid, errors: ${errors.length}, warnings: ${warnings.length})';
}

/// Mixin to add configuration validation capabilities
mixin ConfigValidatorMixin {
  /// Validate configuration with logging
  ValidationResult validateConfigWithLogging(VaultwardenConfig config) {
    final result = ConfigValidator.validateConfig(config);
    
    if (result.errors.isNotEmpty) {
      LoggingService.error('Configuration validation failed: ${result.errors.join(', ')}');
    }
    
    if (result.warnings.isNotEmpty) {
      LoggingService.warning('Configuration warnings: ${result.warnings.join(', ')}');
    }
    
    return result;
  }
}
