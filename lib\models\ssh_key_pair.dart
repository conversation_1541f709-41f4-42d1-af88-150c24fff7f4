/// Model for SSH key pair data
class SSHKeyPair {
  final String publicKey;
  final String privateKey;
  final String fingerprint;
  final DateTime createdAt;

  const SSHKeyPair({
    required this.publicKey,
    required this.privateKey,
    required this.fingerprint,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'publicKey': publicKey,
      'privateKey': privateKey,
      'fingerprint': fingerprint,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory SSHKeyPair.fromJson(Map<String, dynamic> json) {
    return SSHKeyPair(
      publicKey: json['publicKey'] as String,
      privateKey: json['privateKey'] as String,
      fingerprint: json['fingerprint'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  SSHKeyPair copyWith({
    String? publicKey,
    String? privateKey,
    String? fingerprint,
    DateTime? createdAt,
  }) {
    return SSHKeyPair(
      publicKey: publicKey ?? this.publicKey,
      privateKey: privateKey ?? this.privateKey,
      fingerprint: fingerprint ?? this.fingerprint,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SSHKeyPair && other.fingerprint == fingerprint;
  }

  @override
  int get hashCode => fingerprint.hashCode;

  @override
  String toString() {
    return 'SSHKeyPair(fingerprint: $fingerprint, createdAt: $createdAt)';
  }
}
