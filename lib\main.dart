import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/vaultwarden_provider.dart';
import 'providers/tunnel_provider.dart';
import 'screens/main_screen.dart';

void main() {
  runApp(const VaulltApp());
}

class VaulltApp extends StatelessWidget {
  const VaulltApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => VaultwardenProvider()),
        ChangeNotifierProvider(create: (context) => TunnelProvider()),
      ],
      child: MaterialApp(
        title: 'Vaullt - Vaultwarden Manager',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.indigo,
            brightness: Brightness.light,
          ),
          useMaterial3: true,
        ),
        darkTheme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.indigo,
            brightness: Brightness.dark,
          ),
          useMaterial3: true,
        ),
        home: const MainScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
