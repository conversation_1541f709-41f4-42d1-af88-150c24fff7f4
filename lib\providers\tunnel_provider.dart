import 'package:flutter/foundation.dart';
import '../services/local_tunnel_service.dart';
import '../models/connected_device.dart';

/// Provider for managing the local tunnel service
class TunnelProvider with ChangeNotifier {
  final LocalTunnelService _tunnelService = LocalTunnelService();
  
  bool _isInitialized = false;
  bool _isLoading = false;
  String? _errorMessage;
  
  // Getters
  LocalTunnelService get tunnelService => _tunnelService;
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  
  // Streams
  Stream<bool> get tunnelStatus => _tunnelService.tunnelStatus;
  Stream<List<ConnectedDevice>> get connectedDevices => _tunnelService.connectedDevices;
  
  /// Initialize the tunnel service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _setLoading(true);
      await _tunnelService.initialize();
      _isInitialized = true;
      _clearError();
    } catch (e) {
      _setError('Failed to initialize tunnel service: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Start tunnel to device
  Future<void> startTunnel(String deviceId) async {
    try {
      _setLoading(true);
      await _tunnelService.startTunnel(deviceId);
      _clearError();
    } catch (e) {
      _setError('Failed to start tunnel: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Stop tunnel to device
  Future<void> stopTunnel(String deviceId) async {
    try {
      _setLoading(true);
      await _tunnelService.stopTunnel(deviceId);
      _clearError();
    } catch (e) {
      _setError('Failed to stop tunnel: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Add new device
  Future<void> addDevice({
    required String id,
    required String name,
    required String ipAddress,
    required String publicKey,
    required int tunnelPort,
  }) async {
    try {
      _setLoading(true);
      await _tunnelService.addDevice(
        id: id,
        name: name,
        ipAddress: ipAddress,
        publicKey: publicKey,
        tunnelPort: tunnelPort,
      );
      _clearError();
    } catch (e) {
      _setError('Failed to add device: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Remove device
  Future<void> removeDevice(String deviceId) async {
    try {
      _setLoading(true);
      await _tunnelService.removeDevice(deviceId);
      _clearError();
    } catch (e) {
      _setError('Failed to remove device: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Generate pairing QR code
  Future<String?> generatePairingQRCode() async {
    try {
      _setLoading(true);
      final qrCode = await _tunnelService.generatePairingQRCode();
      _clearError();
      return qrCode;
    } catch (e) {
      _setError('Failed to generate QR code: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }
  
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  @override
  void dispose() {
    _tunnelService.dispose();
    super.dispose();
  }
}
