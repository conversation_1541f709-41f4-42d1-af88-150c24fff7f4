import 'package:process_run/shell.dart';
import 'logging_service.dart';
import '../utils/error_handler.dart';

/// Service class for Docker operations
/// Provides robust Docker container management with comprehensive error handling
class DockerService with LoggableMixin, ErrorHandlerMixin {
  final String dockerComposePath;
  final Shell _shell;

  DockerService({required this.dockerComposePath}) : _shell = Shell() {
    logInfo('DockerService initialized with compose path: $dockerComposePath');
  }

  /// Check if the Vaultwarden container is running
  Future<bool> isContainerRunning() async {
    try {
      logDebug('Checking container status...');
      final result = await _shell.run('docker-compose -f "$dockerComposePath" ps -q vaultwarden');

      if (result.isNotEmpty && result.first.stdout.toString().trim().isNotEmpty) {
        final containerId = result.first.stdout.toString().trim();
        logDebug('Found container ID: $containerId');

        final statusResult = await _shell.run('docker inspect --format="{{.State.Running}}" $containerId');
        final isRunning = statusResult.first.stdout.toString().trim() == 'true';

        logDebug('Container running status: $isRunning');
        return isRunning;
      }

      logDebug('No container found');
      return false;
    } catch (e, stackTrace) {
      logAndHandleError(e, context: 'checking container status', stackTrace: stackTrace);
      return false;
    }
  }

  /// Start the Vaultwarden container
  Future<void> startContainer() async {
    try {
      logInfo('Starting Vaultwarden container...');
      await _shell.run('docker-compose -f "$dockerComposePath" up -d');
      logInfo('Container start command executed successfully');
    } catch (e, stackTrace) {
      final exception = handleError(e, context: 'starting container', stackTrace: stackTrace);
      logError('Failed to start container', error: exception, stackTrace: stackTrace);
      throw exception;
    }
  }

  /// Stop the Vaultwarden container
  Future<void> stopContainer() async {
    try {
      logInfo('Stopping Vaultwarden container...');
      await _shell.run('docker-compose -f "$dockerComposePath" down');
      logInfo('Container stop command executed successfully');
    } catch (e, stackTrace) {
      final exception = handleError(e, context: 'stopping container', stackTrace: stackTrace);
      logError('Failed to stop container', error: exception, stackTrace: stackTrace);
      throw exception;
    }
  }

  /// Restart the container (useful after configuration changes)
  Future<void> restartContainer() async {
    try {
      logInfo('Restarting Vaultwarden container...');
      await stopContainer();

      // Small delay to ensure container is fully stopped
      logDebug('Waiting for container to fully stop...');
      await Future.delayed(const Duration(seconds: 2));

      await startContainer();
      logInfo('Container restart completed successfully');
    } catch (e, stackTrace) {
      final exception = handleError(e, context: 'restarting container', stackTrace: stackTrace);
      logError('Failed to restart container', error: exception, stackTrace: stackTrace);
      throw exception;
    }
  }

  /// Get container logs
  Future<String> getLogs({int lines = 100}) async {
    try {
      logDebug('Retrieving container logs (last $lines lines)...');
      final result = await _shell.run('docker-compose -f "$dockerComposePath" logs --tail $lines vaultwarden');
      final logs = result.first.stdout.toString();
      logDebug('Successfully retrieved ${logs.split('\n').length} lines of logs');
      return logs;
    } catch (e, stackTrace) {
      final errorMsg = 'Error retrieving logs: $e';
      logAndHandleError(e, context: 'retrieving container logs', stackTrace: stackTrace);
      return errorMsg;
    }
  }

  /// Check if Docker is installed and running
  Future<bool> isDockerAvailable() async {
    try {
      logDebug('Checking Docker availability...');

      // Check Docker version
      final dockerResult = await _shell.run('docker --version');
      final dockerVersion = dockerResult.first.stdout.toString().trim();
      logDebug('Docker version: $dockerVersion');

      // Check Docker Compose version
      final composeResult = await _shell.run('docker-compose --version');
      final composeVersion = composeResult.first.stdout.toString().trim();
      logDebug('Docker Compose version: $composeVersion');

      logInfo('Docker and Docker Compose are available');
      return true;
    } catch (e) {
      logWarning('Docker or Docker Compose not available', error: e);
      return false;
    }
  }
}
