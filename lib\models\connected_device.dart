/// Model for a connected mobile device
class ConnectedDevice {
  final String id;
  final String name;
  final String ipAddress;
  final String publicKeyFingerprint;
  final DateTime lastConnected;
  final DateTime firstConnected;
  final bool isCurrentlyConnected;
  final int tunnelPort;

  const ConnectedDevice({
    required this.id,
    required this.name,
    required this.ipAddress,
    required this.publicKeyFingerprint,
    required this.lastConnected,
    required this.firstConnected,
    required this.isCurrentlyConnected,
    required this.tunnelPort,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'ipAddress': ipAddress,
      'publicKeyFingerprint': publicKeyFingerprint,
      'lastConnected': lastConnected.toIso8601String(),
      'firstConnected': firstConnected.toIso8601String(),
      'isCurrentlyConnected': isCurrentlyConnected,
      'tunnelPort': tunnelPort,
    };
  }

  factory ConnectedDevice.fromJson(Map<String, dynamic> json) {
    return ConnectedDevice(
      id: json['id'] as String,
      name: json['name'] as String,
      ipAddress: json['ipAddress'] as String,
      publicKeyFingerprint: json['publicKeyFingerprint'] as String,
      lastConnected: DateTime.parse(json['lastConnected'] as String),
      firstConnected: DateTime.parse(json['firstConnected'] as String),
      isCurrentlyConnected: json['isCurrentlyConnected'] as bool,
      tunnelPort: json['tunnelPort'] as int,
    );
  }

  ConnectedDevice copyWith({
    String? id,
    String? name,
    String? ipAddress,
    String? publicKeyFingerprint,
    DateTime? lastConnected,
    DateTime? firstConnected,
    bool? isCurrentlyConnected,
    int? tunnelPort,
  }) {
    return ConnectedDevice(
      id: id ?? this.id,
      name: name ?? this.name,
      ipAddress: ipAddress ?? this.ipAddress,
      publicKeyFingerprint: publicKeyFingerprint ?? this.publicKeyFingerprint,
      lastConnected: lastConnected ?? this.lastConnected,
      firstConnected: firstConnected ?? this.firstConnected,
      isCurrentlyConnected: isCurrentlyConnected ?? this.isCurrentlyConnected,
      tunnelPort: tunnelPort ?? this.tunnelPort,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConnectedDevice && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ConnectedDevice(id: $id, name: $name, ipAddress: $ipAddress, isConnected: $isCurrentlyConnected)';
  }
}
