import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:cryptography/cryptography.dart';
import '../models/ssh_key_pair.dart';

/// Service for managing SSH keys and authentication
class SSHService {
  static const String _keyPairStorageKey = 'ssh_key_pair';
  static const String _authorizedKeysKey = 'authorized_ssh_keys';
  
  final FlutterSecureStorage _secureStorage;
  final Ed25519 _algorithm = Ed25519();

  SSHService({FlutterSecureStorage? secureStorage})
      : _secureStorage = secureStorage ?? const FlutterSecureStorage();

  /// Generate a new SSH key pair for the desktop
  Future<SSHKeyPair> generateKeyPair() async {
    final keyPair = await _algorithm.newKeyPair();
    final privateKeyBytes = await keyPair.extractPrivateKeyBytes();
    final publicKey = await keyPair.extractPublicKey();
    final publicKeyBytes = publicKey.bytes;
    
    // Generate SSH-compatible key strings
    final privateKeyPem = _bytesToPem(privateKeyBytes, 'PRIVATE KEY');
    final publicKeyOpenSSH = _publicKeyToOpenSSH(publicKeyBytes);
    
    // Generate fingerprint
    final fingerprint = await _generateFingerprint(publicKeyBytes);
    
    final sshKeyPair = SSHKeyPair(
      publicKey: publicKeyOpenSSH,
      privateKey: privateKeyPem,
      fingerprint: fingerprint,
      createdAt: DateTime.now(),
    );
    
    // Store the key pair securely
    await _secureStorage.write(
      key: _keyPairStorageKey,
      value: jsonEncode(sshKeyPair.toJson()),
    );
    
    return sshKeyPair;
  }

  /// Get the current SSH key pair, generating one if it doesn't exist
  Future<SSHKeyPair?> getKeyPair() async {
    final keyPairJson = await _secureStorage.read(key: _keyPairStorageKey);
    if (keyPairJson == null) {
      return null;
    }
    
    try {
      final keyPairMap = jsonDecode(keyPairJson) as Map<String, dynamic>;
      return SSHKeyPair.fromJson(keyPairMap);
    } catch (e) {
      // If we can't parse the stored key pair, generate a new one
      return null;
    }
  }

  /// Ensure we have a key pair, generating one if needed
  Future<SSHKeyPair> ensureKeyPair() async {
    var keyPair = await getKeyPair();
    keyPair ??= await generateKeyPair();
    return keyPair;
  }

  /// Add a mobile device's public key to authorized keys
  Future<void> addAuthorizedKey(String deviceId, String publicKey) async {
    final authorizedKeys = await getAuthorizedKeys();
    authorizedKeys[deviceId] = publicKey;
    
    await _secureStorage.write(
      key: _authorizedKeysKey,
      value: jsonEncode(authorizedKeys),
    );
  }

  /// Remove a mobile device's public key from authorized keys
  Future<void> removeAuthorizedKey(String deviceId) async {
    final authorizedKeys = await getAuthorizedKeys();
    authorizedKeys.remove(deviceId);
    
    await _secureStorage.write(
      key: _authorizedKeysKey,
      value: jsonEncode(authorizedKeys),
    );
  }

  /// Get all authorized keys
  Future<Map<String, String>> getAuthorizedKeys() async {
    final authorizedKeysJson = await _secureStorage.read(key: _authorizedKeysKey);
    if (authorizedKeysJson == null) {
      return {};
    }
    
    try {
      final authorizedKeysMap = jsonDecode(authorizedKeysJson) as Map<String, dynamic>;
      return authorizedKeysMap.cast<String, String>();
    } catch (e) {
      return {};
    }
  }

  /// Check if a device is authorized
  Future<bool> isDeviceAuthorized(String deviceId) async {
    final authorizedKeys = await getAuthorizedKeys();
    return authorizedKeys.containsKey(deviceId);
  }

  /// Verify a public key signature (simplified verification)
  Future<bool> verifyPublicKey(String publicKey, String signature, String data) async {
    // This is a simplified verification - in a real implementation,
    // you'd want to properly verify the signature using the public key
    try {
      // For now, just check if the public key is in authorized keys
      final authorizedKeys = await getAuthorizedKeys();
      return authorizedKeys.values.contains(publicKey);
    } catch (e) {
      return false;
    }
  }

  /// Convert bytes to PEM format
  String _bytesToPem(List<int> bytes, String label) {
    final base64String = base64Encode(bytes);
    final buffer = StringBuffer();
    buffer.writeln('-----BEGIN $label-----');
    
    // Split into 64-character lines
    for (int i = 0; i < base64String.length; i += 64) {
      final end = (i + 64 < base64String.length) ? i + 64 : base64String.length;
      buffer.writeln(base64String.substring(i, end));
    }
    
    buffer.writeln('-----END $label-----');
    return buffer.toString();
  }

  /// Convert public key bytes to OpenSSH format
  String _publicKeyToOpenSSH(List<int> publicKeyBytes) {
    final base64Key = base64Encode(publicKeyBytes);
    return 'ssh-ed25519 $base64Key vaullt-desktop@local';
  }

  /// Generate a fingerprint for the public key
  Future<String> _generateFingerprint(List<int> publicKeyBytes) async {
    // Use SHA-256 to generate fingerprint
    final sha256 = Sha256();
    final hash = await sha256.hash(publicKeyBytes);
    
    // Convert to hex string with colons
    final hexBytes = hash.bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).toList();
    final fingerprint = StringBuffer();
    for (int i = 0; i < hexBytes.length; i++) {
      if (i > 0) fingerprint.write(':');
      fingerprint.write(hexBytes[i]);
    }
    
    return 'SHA256:${base64Encode(hash.bytes)}';
  }

  /// Delete all stored keys and authorized keys
  Future<void> clearAllKeys() async {
    await _secureStorage.delete(key: _keyPairStorageKey);
    await _secureStorage.delete(key: _authorizedKeysKey);
  }
}
