import 'dart:async';
import 'dart:convert';
import 'package:dartssh2/dartssh2.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/connected_device.dart';
import 'ssh_service.dart';
import 'network_discovery_service.dart';
import 'logging_service.dart';

/// Service for managing SSH tunnels to mobile devices
/// Provides secure SSH connections with automatic reconnection and error recovery
class LocalTunnelService extends ChangeNotifier with LoggableMixin {
  static const String _connectedDevicesKey = 'connected_devices';
  static const int _defaultSSHPort = 22;
  static const int _baseForwardPort = 8080;
  
  final SSHService _sshService;
  final NetworkDiscoveryService _discoveryService;
  final FlutterSecureStorage _secureStorage;
  
  // State
  final Map<String, SSHClient> _activeConnections = {};
  final Map<String, StreamSubscription> _connectionSubscriptions = {};
  final Map<String, Timer> _reconnectionTimers = {};
  final StreamController<bool> _tunnelStatusController = StreamController<bool>.broadcast();
  final StreamController<List<ConnectedDevice>> _devicesController = StreamController<List<ConnectedDevice>>.broadcast();
  
  bool _isStarted = false;
  List<ConnectedDevice> _connectedDevices = [];

  /// Stream of tunnel availability status
  Stream<bool> get tunnelStatus => _tunnelStatusController.stream;
  
  /// Stream of connected devices
  Stream<List<ConnectedDevice>> get connectedDevices => _devicesController.stream;
  
  /// Current connected devices
  List<ConnectedDevice> get devices => List.unmodifiable(_connectedDevices);
  
  /// Whether any tunnel is currently active
  bool get hasTunnelActive => _activeConnections.isNotEmpty;

  LocalTunnelService({
    SSHService? sshService,
    NetworkDiscoveryService? discoveryService,
    FlutterSecureStorage? secureStorage,
  }) : _sshService = sshService ?? SSHService(),
       _discoveryService = discoveryService ?? NetworkDiscoveryService(),
       _secureStorage = secureStorage ?? const FlutterSecureStorage();

  /// Initialize the tunnel service
  Future<void> initialize() async {
    if (_isStarted) return;

    try {
      logInfo('Initializing LocalTunnelService...');

      // Load connected devices from storage
      await _loadConnectedDevices();
      logDebug('Loaded ${_connectedDevices.length} connected devices from storage');

      // Ensure we have SSH keys
      await _sshService.ensureKeyPair();
      logDebug('SSH key pair verified');

      // Start network discovery
      await _discoveryService.startDiscovery();
      logDebug('Network discovery service started');

      // Start SSH server for incoming connections
      await _startSSHServer();
      logDebug('SSH server started');

      _isStarted = true;
      notifyListeners();

      logInfo('LocalTunnelService initialized successfully');
    } catch (e, stackTrace) {
      logError('Failed to initialize LocalTunnelService', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Start a tunnel to a specific device
  Future<void> startTunnel(String deviceId) async {
    final device = _connectedDevices.firstWhere(
      (d) => d.id == deviceId,
      orElse: () => throw Exception('Device not found: $deviceId'),
    );

    if (_activeConnections.containsKey(deviceId)) {
      logWarning('Tunnel already active for device: ${device.name}');
      return;
    }

    try {
      logInfo('Starting SSH tunnel to ${device.name} (${device.ipAddress})');

      // Create SSH client
      final socket = await SSHSocket.connect(device.ipAddress, _defaultSSHPort);
      final client = SSHClient(
        socket,
        username: 'vaultwarden-mobile',
        onPasswordRequest: () => null, // We only use key-based auth
      );

      // Wait for authentication to complete
      await client.authenticated;
      logDebug('SSH authentication completed for ${device.name}');

      // Create port forward (mobile device forwards its localhost:8080 to our Vaultwarden port)
      await client.forwardLocal('localhost', _baseForwardPort);
      logDebug('Port forwarding established: localhost:$_baseForwardPort');

      _activeConnections[deviceId] = client;

      // Update device status
      final updatedDevice = device.copyWith(
        isCurrentlyConnected: true,
        lastConnected: DateTime.now(),
      );

      _updateDeviceInList(updatedDevice);

      // Monitor connection health
      _monitorConnection(deviceId, client);

      logInfo('SSH tunnel established successfully to ${device.name}');
      _tunnelStatusController.add(true);

    } catch (e, stackTrace) {
      logError('Failed to start tunnel to ${device.name}', error: e, stackTrace: stackTrace);
      await _cleanupConnection(deviceId);

      // Schedule reconnection attempt
      _scheduleReconnection(deviceId);
      rethrow;
    }
  }

  /// Stop a tunnel to a specific device
  Future<void> stopTunnel(String deviceId) async {
    final device = _connectedDevices.firstWhere(
      (d) => d.id == deviceId,
      orElse: () => throw Exception('Device not found: $deviceId'),
    );

    await _cleanupConnection(deviceId);

    // Update device status
    final updatedDevice = device.copyWith(isCurrentlyConnected: false);
    _updateDeviceInList(updatedDevice);

    logInfo('SSH tunnel stopped for ${device.name}');

    if (_activeConnections.isEmpty) {
      _tunnelStatusController.add(false);
      logDebug('All tunnels stopped');
    }
  }

  /// Stop all active tunnels
  Future<void> stopAllTunnels() async {
    final deviceIds = List.of(_activeConnections.keys);
    
    for (final deviceId in deviceIds) {
      await stopTunnel(deviceId);
    }
  }

  /// Add a new device (typically called after QR code scanning)
  Future<void> addDevice({
    required String id,
    required String name,
    required String ipAddress,
    required String publicKey,
    required int tunnelPort,
  }) async {
    // Generate fingerprint for the public key
    final fingerprint = 'SHA256:${base64Encode(publicKey.codeUnits)}'; // Simplified fingerprint
    
    final device = ConnectedDevice(
      id: id,
      name: name,
      ipAddress: ipAddress,
      publicKeyFingerprint: fingerprint,
      firstConnected: DateTime.now(),
      lastConnected: DateTime.now(),
      isCurrentlyConnected: false,
      tunnelPort: tunnelPort,
    );

    // Add to authorized keys
    await _sshService.addAuthorizedKey(id, publicKey);
    
    // Add to device list
    _connectedDevices.add(device);
    await _saveConnectedDevices();

    _devicesController.add(_connectedDevices);
    notifyListeners();

    logInfo('Added new device: ${device.name} (${device.ipAddress})');
  }

  /// Remove a device
  Future<void> removeDevice(String deviceId) async {
    // Get device name before removal for logging
    final deviceName = _connectedDevices.where((d) => d.id == deviceId).isNotEmpty
        ? _connectedDevices.firstWhere((d) => d.id == deviceId).name
        : 'Unknown Device';

    try {
      // Stop tunnel if active
      if (_activeConnections.containsKey(deviceId)) {
        await stopTunnel(deviceId);
      }

      // Remove from authorized keys
      await _sshService.removeAuthorizedKey(deviceId);

      // Remove from device list
      _connectedDevices.removeWhere((d) => d.id == deviceId);
      await _saveConnectedDevices();

      _devicesController.add(_connectedDevices);
      notifyListeners();

      logInfo('Removed device: $deviceName');
    } catch (e, stackTrace) {
      logError('Failed to remove device: $deviceName', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Generate QR code data for device pairing
  Future<String> generatePairingQRCode({String? hostAddress}) async {
    final keyPair = await _sshService.ensureKeyPair();

    // Use provided host address or default to localhost
    final host = hostAddress ?? 'localhost';

    final pairingData = {
      'type': 'vaultwarden_pairing',
      'version': '1.0',
      'desktop_ip': host,
      'ssh_port': _defaultSSHPort,
      'public_key': keyPair.publicKey,
      'fingerprint': keyPair.fingerprint,
    };

    logInfo('Generated pairing QR code for host: $host');
    return jsonEncode(pairingData);
  }

  /// Start SSH server for incoming connections
  Future<void> _startSSHServer() async {
    // This would start an SSH server to accept incoming connections
    // For now, we'll simulate this with a placeholder
    logInfo('SSH server started on port $_defaultSSHPort');

    // In a real implementation, you would:
    // 1. Start an SSH server daemon
    // 2. Configure it to only accept key-based authentication
    // 3. Use the authorized keys from SSHService
  }

  /// Monitor connection health and handle reconnections
  void _monitorConnection(String deviceId, SSHClient client) {
    final subscription = Stream.periodic(const Duration(seconds: 30))
        .listen((_) async {
      try {
        // Simple keep-alive check
        if (!client.isClosed) {
          // Connection is still active
          return;
        }
      } catch (e) {
        logWarning('Connection lost to device $deviceId', error: e);
      }

      // Connection lost, clean up and schedule reconnection
      await _cleanupConnection(deviceId);
      _scheduleReconnection(deviceId);
    });

    _connectionSubscriptions[deviceId] = subscription;
  }

  /// Schedule automatic reconnection
  void _scheduleReconnection(String deviceId, {int delaySeconds = 30}) {
    _reconnectionTimers[deviceId]?.cancel();

    _reconnectionTimers[deviceId] = Timer(Duration(seconds: delaySeconds), () async {
      if (!_activeConnections.containsKey(deviceId)) {
        try {
          logInfo('Attempting to reconnect to device $deviceId');
          await startTunnel(deviceId);
        } catch (e) {
          logWarning('Reconnection failed for device $deviceId', error: e);
          // Schedule another reconnection attempt with exponential backoff
          final nextDelay = (delaySeconds * 1.5).round().clamp(30, 300);
          logDebug('Scheduling next reconnection attempt in ${nextDelay}s');
          _scheduleReconnection(deviceId, delaySeconds: nextDelay);
        }
      }
    });
  }

  /// Clean up connection resources
  Future<void> _cleanupConnection(String deviceId) async {
    final client = _activeConnections.remove(deviceId);
    if (client != null && !client.isClosed) {
      try {
        client.close();
        logDebug('SSH client closed for device $deviceId');
      } catch (e) {
        logWarning('Error closing SSH client for device $deviceId', error: e);
      }
    }

    _connectionSubscriptions[deviceId]?.cancel();
    _connectionSubscriptions.remove(deviceId);

    _reconnectionTimers[deviceId]?.cancel();
    _reconnectionTimers.remove(deviceId);

    logDebug('Connection cleanup completed for device $deviceId');
  }

  /// Load connected devices from secure storage
  Future<void> _loadConnectedDevices() async {
    try {
      final devicesJson = await _secureStorage.read(key: _connectedDevicesKey);
      if (devicesJson != null) {
        final devicesList = jsonDecode(devicesJson) as List;
        _connectedDevices = devicesList
            .map((json) => ConnectedDevice.fromJson(json as Map<String, dynamic>))
            .toList();

        _devicesController.add(_connectedDevices);
        logDebug('Loaded ${_connectedDevices.length} devices from secure storage');
      } else {
        logDebug('No saved devices found in secure storage');
      }
    } catch (e, stackTrace) {
      logError('Error loading connected devices', error: e, stackTrace: stackTrace);
      _connectedDevices = [];
    }
  }

  /// Save connected devices to secure storage
  Future<void> _saveConnectedDevices() async {
    try {
      final devicesJson = jsonEncode(_connectedDevices.map((d) => d.toJson()).toList());
      await _secureStorage.write(key: _connectedDevicesKey, value: devicesJson);
      logDebug('Saved ${_connectedDevices.length} devices to secure storage');
    } catch (e, stackTrace) {
      logError('Error saving connected devices', error: e, stackTrace: stackTrace);
    }
  }

  /// Update a device in the list
  void _updateDeviceInList(ConnectedDevice updatedDevice) {
    final index = _connectedDevices.indexWhere((d) => d.id == updatedDevice.id);
    if (index != -1) {
      _connectedDevices[index] = updatedDevice;
      _saveConnectedDevices();
      _devicesController.add(_connectedDevices);
      notifyListeners();
    }
  }

  @override
  void dispose() {
    stopAllTunnels();
    _discoveryService.dispose();
    _tunnelStatusController.close();
    _devicesController.close();
    
    // Clean up all timers
    for (final timer in _reconnectionTimers.values) {
      timer.cancel();
    }
    _reconnectionTimers.clear();
    
    // Clean up all subscriptions
    for (final subscription in _connectionSubscriptions.values) {
      subscription.cancel();
    }
    _connectionSubscriptions.clear();
    
    super.dispose();
  }
}
