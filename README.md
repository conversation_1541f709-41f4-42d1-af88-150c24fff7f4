# VaAulLT - Vaultwarden Desktop Manager

A Flutter desktop application for managing Vaultwarden instances with secure SSH tunneling capabilities for mobile device access.

## Features

- **Docker Container Management**: Start, stop, and monitor Vaultwarden containers
- **SSH Tunneling**: Secure connections between mobile devices and desktop Vaultwarden instances
- **Network Discovery**: Automatic discovery of Vaultwarden devices on the local network (with Windows compatibility)
- **Configuration Management**: Easy configuration of Vaultwarden settings
- **Cross-Platform**: Supports Windows, macOS, and Linux
- **Secure Storage**: Encrypted storage of SSH keys and device configurations

## Architecture

### Core Components

- **Services Layer**: Business logic and external integrations
  - `DockerService`: Docker container management
  - `LocalTunnelService`: SSH tunnel management
  - `NetworkDiscoveryService`: Device discovery with Windows compatibility
  - `SSHService`: SSH key management and authentication
  - `ConfigService`: Configuration file management
  - `LoggingService`: Centralized logging system

- **Providers Layer**: State management using Provider pattern
  - `VaultwardenProvider`: Vaultwarden container state
  - `TunnelProvider`: SSH tunnel state management

- **Models Layer**: Data structures
  - `VaultwardenConfig`: Configuration model
  - `ConnectedDevice`: Device connection information
  - `SSHKeyPair`: SSH key pair data

- **Utils Layer**: Utility functions and helpers
  - `ErrorHandler`: Centralized error handling
  - `ConfigValidator`: Configuration validation

### Key Improvements Made

1. **Windows Compatibility**: Fixed mDNS issues on Windows with fallback network scanning
2. **Robust Error Handling**: Comprehensive error categorization and recovery
3. **Structured Logging**: Professional logging system with different levels
4. **Configuration Validation**: Input validation and sanitization
5. **Code Organization**: Clear separation of concerns and consistent patterns

## Installation

### Prerequisites

- Flutter SDK (3.9.0 or higher)
- Docker Desktop
- Docker Compose

### Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd VaAulLT
   ```

2. Install dependencies:
   ```bash
   flutter pub get
   ```

3. Ensure Docker is running:
   ```bash
   docker --version
   docker-compose --version
   ```

4. Run the application:
   ```bash
   flutter run -d windows  # For Windows
   flutter run -d macos    # For macOS
   flutter run -d linux    # For Linux
   ```

## Configuration

### Default Settings

The application uses secure defaults:
- **Port**: 11001 (non-standard to avoid conflicts)
- **Signups**: Disabled by default
- **Network Access**: Localhost only by default
- **Data Path**: Platform-appropriate user directory

### Customization

You can modify settings through the UI or by editing the Docker Compose file:
- Location: `assets/vaultwarden/docker-compose.yaml`
- The application will validate and apply changes automatically

## Usage

### Starting Vaultwarden

1. Launch the application
2. Click "Start Container" to begin the Vaultwarden service
3. Access the web interface at `http://localhost:11001`

### Mobile Device Pairing

1. Generate a QR code from the "Devices" tab
2. Scan the QR code with your mobile device
3. The SSH tunnel will be established automatically
4. Your mobile device can now access Vaultwarden securely

### Troubleshooting

#### Windows-Specific Issues

- **mDNS Errors**: The application automatically falls back to network scanning on Windows
- **Port Conflicts**: Change the port in settings if 11001 is already in use
- **Docker Issues**: Ensure Docker Desktop is running and WSL2 is enabled

#### Common Problems

- **Container Won't Start**: Check Docker logs and ensure no port conflicts
- **SSH Connection Failed**: Verify network connectivity and firewall settings
- **Configuration Errors**: Use the built-in validation to identify issues

## Development

### Project Structure

```
lib/
├── main.dart                 # Application entry point
├── models/                   # Data models
│   ├── connected_device.dart
│   ├── ssh_key_pair.dart
│   └── vaultwarden_config.dart
├── providers/                # State management
│   ├── tunnel_provider.dart
│   └── vaultwarden_provider.dart
├── screens/                  # UI screens
├── services/                 # Business logic
│   ├── config_service.dart
│   ├── docker_service.dart
│   ├── local_tunnel_service.dart
│   ├── logging_service.dart
│   ├── network_discovery_service.dart
│   └── ssh_service.dart
├── utils/                    # Utilities
│   ├── config_validator.dart
│   └── error_handler.dart
└── widgets/                  # Reusable UI components
```

### Contributing

1. Follow the established patterns for error handling and logging
2. Add comprehensive tests for new features
3. Validate configurations before applying changes
4. Use the logging service instead of print statements
5. Handle platform-specific differences appropriately

## Security Considerations

- SSH keys are stored securely using Flutter Secure Storage
- Default configuration prioritizes security over convenience
- Network access is restricted to localhost by default
- All connections use key-based authentication

## License

[Add your license information here]

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
