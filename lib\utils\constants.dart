/// Application-wide constants and configuration values
class AppConstants {
  // Application Information
  static const String appName = 'VaAulLT';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Vaultwarden Desktop Manager';

  // Network Configuration
  static const int defaultVaultwardenPort = 11001;
  static const int defaultSSHPort = 22;
  static const int baseForwardPort = 8080;
  static const String defaultBindAddress = '127.0.0.1';
  
  // Service Discovery
  static const String mdnsServiceType = '_vaultwarden-desktop._tcp';
  static const Duration discoveryInterval = Duration(seconds: 10);
  static const Duration networkScanInterval = Duration(seconds: 30);
  
  // Connection Management
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration keepAliveInterval = Duration(seconds: 30);
  static const Duration reconnectionDelay = Duration(seconds: 30);
  static const int maxReconnectionDelay = 300; // seconds
  static const double reconnectionBackoffMultiplier = 1.5;
  
  // Container Management
  static const Duration containerStartupDelay = Duration(seconds: 3);
  static const Duration containerShutdownDelay = Duration(seconds: 2);
  static const int defaultLogLines = 100;
  
  // Security
  static const String sshUsername = 'vaultwarden-mobile';
  static const String keyAlgorithm = 'ed25519';
  
  // Storage Keys
  static const String connectedDevicesKey = 'connected_devices';
  static const String sshKeyPairKey = 'ssh_key_pair';
  static const String authorizedKeysKey = 'authorized_ssh_keys';
  
  // File Paths
  static const String dockerComposeRelativePath = 'assets/vaultwarden/docker-compose.yaml';
  static const String vaultwardenDataDir = 'vw-data';
  
  // Validation Limits
  static const int minPort = 1;
  static const int maxPort = 65535;
  static const int privilegedPortThreshold = 1024;
  
  // Common Ports (for validation warnings)
  static const Map<int, String> commonPorts = {
    22: 'SSH',
    23: 'Telnet',
    25: 'SMTP',
    53: 'DNS',
    80: 'HTTP',
    110: 'POP3',
    143: 'IMAP',
    443: 'HTTPS',
    993: 'IMAPS',
    995: 'POP3S',
    3389: 'RDP',
    5432: 'PostgreSQL',
    3306: 'MySQL',
    6379: 'Redis',
    27017: 'MongoDB',
  };
  
  // Error Messages
  static const String dockerNotAvailableError = 'Docker or Docker Compose is not installed or not running';
  static const String containerNotFoundError = 'Vaultwarden container not found';
  static const String networkConnectionError = 'Network connection failed';
  static const String sshAuthenticationError = 'SSH authentication failed';
  static const String configurationError = 'Configuration validation failed';
  
  // Success Messages
  static const String containerStartedMessage = 'Vaultwarden container started successfully';
  static const String containerStoppedMessage = 'Vaultwarden container stopped successfully';
  static const String tunnelEstablishedMessage = 'SSH tunnel established successfully';
  static const String deviceAddedMessage = 'Device added successfully';
  static const String deviceRemovedMessage = 'Device removed successfully';
}

/// Platform-specific constants
class PlatformConstants {
  // Windows-specific
  static const String windowsAppDataEnvVar = 'APPDATA';
  static const String windowsUserProfileEnvVar = 'USERPROFILE';
  static const String windowsDefaultDataPath = 'VaAulLT\\vaultwarden-data';
  
  // macOS-specific
  static const String macOSHomeEnvVar = 'HOME';
  static const String macOSDataPath = 'Library/Application Support/VaAulLT/vaultwarden-data';
  
  // Linux-specific
  static const String linuxHomeEnvVar = 'HOME';
  static const String linuxDataPath = '.local/share/VaAulLT/vaultwarden-data';
  
  // Fallback paths
  static const String fallbackWindowsPath = 'C:\\';
  static const String fallbackUnixPath = '/tmp';
}

/// UI Constants
class UIConstants {
  // Spacing
  static const double smallSpacing = 8.0;
  static const double mediumSpacing = 16.0;
  static const double largeSpacing = 24.0;
  static const double extraLargeSpacing = 32.0;
  
  // Border Radius
  static const double smallRadius = 4.0;
  static const double mediumRadius = 8.0;
  static const double largeRadius = 12.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Icon Sizes
  static const double smallIconSize = 16.0;
  static const double mediumIconSize = 24.0;
  static const double largeIconSize = 32.0;
  
  // Button Heights
  static const double buttonHeight = 48.0;
  static const double smallButtonHeight = 36.0;
  static const double largeButtonHeight = 56.0;
}

/// Logging Constants
class LoggingConstants {
  // Log Levels (matching dart:developer levels)
  static const int debugLevel = 500;
  static const int infoLevel = 800;
  static const int warningLevel = 900;
  static const int errorLevel = 1000;
  
  // Log Tags
  static const String networkTag = 'NETWORK';
  static const String dockerTag = 'DOCKER';
  static const String sshTag = 'SSH';
  static const String configTag = 'CONFIG';
  static const String tunnelTag = 'TUNNEL';
  static const String serviceTag = 'SERVICE';
  static const String uiTag = 'UI';
}

/// Development Constants
class DevConstants {
  // Debug flags
  static const bool enableVerboseLogging = true;
  static const bool enableNetworkLogging = true;
  static const bool enablePerformanceLogging = false;
  
  // Test data
  static const String testDeviceId = 'test-device-001';
  static const String testDeviceName = 'Test Mobile Device';
  static const String testIPAddress = '*************';
  
  // Mock delays (for testing)
  static const Duration mockNetworkDelay = Duration(milliseconds: 500);
  static const Duration mockContainerStartDelay = Duration(seconds: 2);
}

/// Regular Expressions for Validation
class ValidationPatterns {
  // IP Address validation
  static final RegExp ipv4Pattern = RegExp(
    r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
  );
  
  // Hostname validation
  static final RegExp hostnamePattern = RegExp(
    r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
  );
  
  // SSH key validation (basic)
  static final RegExp sshKeyPattern = RegExp(
    r'^ssh-(rsa|dss|ed25519|ecdsa) [A-Za-z0-9+/]+=* .*$'
  );
  
  // Device ID validation
  static final RegExp deviceIdPattern = RegExp(
    r'^[a-zA-Z0-9\-_]{1,64}$'
  );
  
  // Path validation (basic)
  static final RegExp pathPattern = RegExp(
    r'^[^<>:"|?*]*$'
  );
}

/// Feature Flags
class FeatureFlags {
  // Network features
  static const bool enableMdnsDiscovery = true;
  static const bool enableNetworkScanning = true;
  static const bool enableAutoReconnection = true;
  
  // Security features
  static const bool enforceKeyAuthentication = true;
  static const bool enableSecureStorage = true;
  
  // UI features
  static const bool enableDarkMode = true;
  static const bool enableAnimations = true;
  static const bool enableNotifications = true;
  
  // Development features
  static const bool enableDebugMode = false;
  static const bool enableMockServices = false;
}
