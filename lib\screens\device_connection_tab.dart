import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import '../models/connected_device.dart';
import '../providers/tunnel_provider.dart';
import '../widgets/device_card.dart';

class DeviceConnectionTab extends StatefulWidget {
  const DeviceConnectionTab({super.key});

  @override
  State<DeviceConnectionTab> createState() => _DeviceConnectionTabState();
}

class _DeviceConnectionTabState extends State<DeviceConnectionTab> {
  String? _pairingQRCode;
  bool _isGeneratingQR = false;
  bool _showQRCode = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final tunnelProvider = Provider.of<TunnelProvider>(context, listen: false);
      if (!tunnelProvider.isInitialized) {
        tunnelProvider.initialize();
      }
    });
  }

  Future<void> _generatePairingQR() async {
    final tunnelProvider = Provider.of<TunnelProvider>(context, listen: false);

    setState(() {
      _isGeneratingQR = true;
    });

    try {
      final qrData = await tunnelProvider.generatePairingQRCode();
      if (qrData != null) {
        setState(() {
          _pairingQRCode = qrData;
          _showQRCode = true;
          _isGeneratingQR = false;
        });
      } else {
        setState(() {
          _isGeneratingQR = false;
        });
        
        if (mounted && tunnelProvider.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(tunnelProvider.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isGeneratingQR = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to generate QR code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TunnelProvider>(
      builder: (context, tunnelProvider, child) {
        if (!tunnelProvider.isInitialized && tunnelProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  const Icon(Icons.devices, size: 32),
                  const SizedBox(width: 16),
                  const Text(
                    'Device Connections',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  StreamBuilder<bool>(
                    stream: tunnelProvider.tunnelStatus,
                    builder: (context, snapshot) {
                      final hasActiveTunnel = snapshot.data ?? false;
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: hasActiveTunnel ? Colors.green : Colors.grey,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              hasActiveTunnel ? Icons.link : Icons.link_off,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              hasActiveTunnel ? 'Connected' : 'Disconnected',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
              
              const SizedBox(height: 24),

              // Add Device Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Add New Device',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Scan this QR code with your mobile Vaultwarden app to establish a secure connection.',
                        style: TextStyle(color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      
                      if (!_showQRCode) ...[
                        Center(
                          child: ElevatedButton.icon(
                            onPressed: _isGeneratingQR ? null : _generatePairingQR,
                            icon: _isGeneratingQR
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Icon(Icons.qr_code),
                            label: Text(_isGeneratingQR ? 'Generating...' : 'Generate QR Code'),
                          ),
                        ),
                      ] else ...[
                        Center(
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: QrImageView(
                              data: _pairingQRCode!,
                              version: QrVersions.auto,
                              size: 200.0,
                              backgroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Center(
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              TextButton.icon(
                                onPressed: _generatePairingQR,
                                icon: const Icon(Icons.refresh),
                                label: const Text('Regenerate'),
                              ),
                              const SizedBox(width: 16),
                              TextButton.icon(
                                onPressed: () {
                                  setState(() {
                                    _showQRCode = false;
                                    _pairingQRCode = null;
                                  });
                                },
                                icon: const Icon(Icons.close),
                                label: const Text('Hide'),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Connected Devices Section
              const Text(
                'Connected Devices',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Devices List
              Expanded(
                child: StreamBuilder<List<ConnectedDevice>>(
                  stream: tunnelProvider.connectedDevices,
                  builder: (context, snapshot) {
                    final devices = snapshot.data ?? [];

                    if (devices.isEmpty) {
                      return const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.phone_android_outlined,
                              size: 64,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'No devices connected',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Generate a QR code above to connect your mobile device',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.builder(
                      itemCount: devices.length,
                      itemBuilder: (context, index) {
                        final device = devices[index];
                        return DeviceCard(
                          device: device,
                          onConnect: () => tunnelProvider.startTunnel(device.id),
                          onDisconnect: () => tunnelProvider.stopTunnel(device.id),
                          onRemove: () => _showRemoveDeviceDialog(device),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showRemoveDeviceDialog(ConnectedDevice device) {
    final tunnelProvider = Provider.of<TunnelProvider>(context, listen: false);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Device'),
        content: Text('Are you sure you want to remove "${device.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              tunnelProvider.removeDevice(device.id);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }
}
