Proposed Architecture

We recommend a multi-process client-server architecture: the Flutter desktop app acts as the GUI, and it launches two local HTTP server processes – one running Vaultwarden (for passwords) and one running Immich (for images). Each service runs on 127.0.0.1 at a fixed port and stores its data locally (e.g. Vaultwarden uses an SQLite file, <PERSON>mm<PERSON> can use SQLite or a bundled Postgres). This follows <PERSON>mm<PERSON>’s own model: “Imm<PERSON> uses a traditional client-server design, with a dedicated database” . The Flutter app simply starts these servers on demand (using dart:io), then communicates with them over localhost. By spawning them as separate processes, we isolate each service and can control their lifecycle (start/stop) from the app.

Bundling and Running Local Servers

To make installation seamless, we bundle each server with the app as self-contained executables. A proven pattern (from, e.g., the Flutter‑Python starter kit) is to package backend code as standalone binaries and ship them as assets in the Flutter app . Concretely:
	•	Vaultwarden: Use its official Rust binaries for Windows, macOS, Linux (downloaded from GitHub releases ). No extra runtime needed.
	•	Immich: Instead of requiring Docker, enable Immich’s SQLite mode (introduced in v1.132 ) so it runs as one Node process. We bundle a NodeJS runtime (or compile <PERSON><PERSON><PERSON> with nexe) and the Immich server code as a single executable.

On first run, the app extracts these executables to a private folder. Then it can start Vaultwarden and Immich in the background (e.g. Process.start("vaultwarden", [...]) and similarly for Immich). This means the user can just download and run the app – nothing else needs to be installed. Fixed versions are ensured because the bundled executables are tied to the app version. The Flutter app can even handle versioning by comparing timestamps and replacing old binaries with new ones from the app bundle .

Note: The Immich documentation normally uses Docker Compose (“docker compose up -d” to launch all services)  , but here we avoid Docker entirely by using SQLite and a single binary. This keeps the install light and cross-platform. Also, by listening on high ports (e.g. 8080, 2283), neither service needs root/administrator privileges at runtime.

UI Integration

For now, the Flutter app will simply embed each service’s web interface. We can use Flutter’s WebView (with plugins like webview_flutter or webview_windows) to load Vaultwarden’s web vault (served by the Vaultwarden process) and Immich’s web portal (served by the Immich process). For example, one tab of the app might show http://localhost:8080 (Vaultwarden UI) and another tab http://localhost:2283 (Immich UI). This immediately leverages their full UIs without extra coding. In future, those views could be replaced by custom Flutter widgets calling the same APIs. Flutter’s desktop WebView support is maturing, so embedding these local pages is straightforward. (The app can also provide a simple settings screen in Flutter itself to adjust options like ports or data paths.)

Packaging & Distribution

We distribute the app as a self-contained desktop package on each platform. Use Flutter’s build tools to produce native installers/bundles: e.g. a Windows EXE/MSIX, a macOS .app (DMG), and a Linux bundle (.AppImage, .deb, etc.). All bundled files (Flutter binaries, Vaultwarden executable, Immich executable, plus UI assets) go into this package. On installation, the app creates a local data directory (for config, databases, photos) under the user’s profile. Users can simply download, run, and start using it – no Docker or additional setup is required.

For updates, treat the app like production software: use native mechanisms or an auto-update library. For instance, on Windows you might publish via an installer or MS Store; on macOS use Sparkle/DMG or notarized updates; on Linux use Snap/Flatpak/AppImage updates. When a new app version is installed, it overwrites the old server binaries with the updated ones (as per the versioning approach ). In this way, the servers stay in lock-step with the app’s releases, and updating the app inherently updates Vaultwarden and Immich to the new fixed versions.

Background Service Management

The Flutter app will manage the servers transparently. On startup, the app checks if the Vaultwarden and Immich processes are running; if not, it launches them. It can monitor their health and restart if needed. In a settings screen, the user could change options (e.g. ports, storage location) – which the app would apply by restarting the services. Since all services are local, we can connect them directly without the internet. (For example, Immich’s mobile app could connect to http://localhost:2283 to back up photos.) Because we bundle fixed versions, we avoid unexpected updates or mismatches between client and server.

Privileges and Security

We avoid any privileged operations. Both servers bind to non-privileged ports (>1024) on localhost, so no elevated permissions are required. Data is kept entirely on disk locally. Vaultwarden encrypts all passwords before storage, and Immich stores photos in a user directory. The Flutter app itself can be sandboxed to the extent possible (and on macOS you’d disable the app sandbox if needed for localhost networking ). Users are admin only to install the app; running it does not require admin/root. If desired, one can also enable HTTPS locally (Vaultwarden supports TLS and Immich can be fronted by a local proxy), but for a local-only setup HTTP on localhost is usually fine.

In summary, this design meets all requirements: a unified Flutter front-end that bundles and auto-updates Vaultwarden and Immich as background processes, exposes their full UIs to the user via WebViews, and runs on any desktop OS with a simple installer. The cited examples confirm this is practical: the Flutter-Python pattern demonstrates bundling executables for desktop  , and Immich’s docs show its normal Docker-based startup  . By adapting those ideas, we get a cross-platform, easy-to-install app with fixed server versions and a straightforward update path.

Sources: Immich architecture & setup docs   ; Vaultwarden releases and packaging practices ; Flutter desktop bundling example  ; Immich SQLite support announcement . Each shows the viability of this local multi-server approach.