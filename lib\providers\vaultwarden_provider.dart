import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'dart:io';
import '../models/vaultwarden_config.dart';
import '../services/docker_service.dart';
import '../services/config_service.dart';
import '../utils/config_validator.dart';

/// State management provider for Vaultwarden operations
class VaultwardenProvider with ChangeNotifier {
  // Services
  late final DockerService _dockerService;
  late final ConfigService _configService;

  // State variables
  VaultwardenConfig _config = const VaultwardenConfig(
    signupsAllowed: false,
    dataPath: '',
    port: 11001,
    localhostOnly: true,
  );

  bool _isLoading = false;
  bool _isInitialized = false;
  String _statusMessage = '';
  String? _errorMessage;

  // Getters
  VaultwardenConfig get config => _config;
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;
  String get statusMessage => _statusMessage;
  String? get errorMessage => _errorMessage;
  bool get isRunning => _config.isRunning;

  // Constructor
  VaultwardenProvider() {
    _initialize();
  }

  /// Initialize the provider
  Future<void> _initialize() async {
    try {
      _setLoading(true);
      _statusMessage = 'Initializing...';

      final dockerComposePath = path.join(
        Directory.current.path,
        'assets',
        'vaultwarden',
        'docker-compose.yaml',
      );

      _dockerService = DockerService(dockerComposePath: dockerComposePath);
      _configService = ConfigService(dockerComposePath: dockerComposePath);

      // Check if Docker is available
      final dockerAvailable = await _dockerService.isDockerAvailable();
      if (!dockerAvailable) {
        throw Exception('Docker or Docker Compose is not installed or not running');
      }

      // Load configuration
      await _loadConfiguration();

      // Check container status
      await _checkContainerStatus();

      _isInitialized = true;
      _statusMessage = 'Ready';
      _clearError();
    } catch (e) {
      _setError('Initialization failed: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load configuration from docker-compose.yaml
  Future<void> _loadConfiguration() async {
    try {
      _config = await _configService.loadConfig();
      notifyListeners();
    } catch (e) {
      throw Exception('Failed to load configuration: $e');
    }
  }

  /// Check current container status
  Future<void> _checkContainerStatus() async {
    try {
      final running = await _dockerService.isContainerRunning();
      _config = _config.copyWith(isRunning: running);
      notifyListeners();
    } catch (e) {
      debugPrint('Error checking container status: $e');
    }
  }

  /// Start the Vaultwarden container
  Future<void> startContainer() async {
    if (_isLoading) return;

    try {
      _setLoading(true);
      _statusMessage = 'Starting Vaultwarden container...';

      await _dockerService.startContainer();
      
      // Wait a moment for container to fully start
      await Future.delayed(const Duration(seconds: 3));
      await _checkContainerStatus();

      _statusMessage = 'Vaultwarden started successfully!';
      _clearError();
    } catch (e) {
      _setError('Failed to start container: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Stop the Vaultwarden container
  Future<void> stopContainer() async {
    if (_isLoading) return;

    try {
      _setLoading(true);
      _statusMessage = 'Stopping Vaultwarden container...';

      await _dockerService.stopContainer();
      
      // Wait a moment for container to fully stop
      await Future.delayed(const Duration(seconds: 2));
      await _checkContainerStatus();

      _statusMessage = 'Vaultwarden stopped successfully!';
      _clearError();
    } catch (e) {
      _setError('Failed to stop container: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update signups allowed setting
  Future<void> updateSignupsAllowed(bool allowed) async {
    if (_isLoading) return;

    try {
      _setLoading(true);
      _statusMessage = 'Updating signup settings...';

      final newConfig = _config.copyWith(signupsAllowed: allowed);
      await _configService.saveConfig(newConfig);
      _config = newConfig;

      // Restart container if it's running to apply changes
      if (_config.isRunning) {
        _statusMessage = 'Restarting container to apply changes...';
        await _dockerService.restartContainer();
        await Future.delayed(const Duration(seconds: 3));
        await _checkContainerStatus();
      }

      _statusMessage = 'Signup settings updated successfully!';
      _clearError();
    } catch (e) {
      _setError('Failed to update signup settings: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update data storage path
  Future<void> updateDataPath(String newPath) async {
    if (_isLoading) return;

    try {
      _setLoading(true);
      _statusMessage = 'Updating data path...';

      final newConfig = _config.copyWith(dataPath: newPath);

      // Use the new validation system
      final validation = ConfigValidator.validateConfig(newConfig);
      if (!validation.isValid) {
        throw Exception('Invalid configuration: ${validation.formattedMessage}');
      }

      await _configService.saveConfig(newConfig);
      _config = newConfig;

      // Restart container if it's running to apply changes
      if (_config.isRunning) {
        _statusMessage = 'Restarting container to apply changes...';
        await _dockerService.restartContainer();
        await Future.delayed(const Duration(seconds: 3));
        await _checkContainerStatus();
      }

      _statusMessage = 'Data path updated successfully!';
      _clearError();
    } catch (e) {
      _setError('Failed to update data path: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update port setting
  Future<void> updatePort(int newPort) async {
    if (_isLoading) return;
    if (newPort < 1 || newPort > 65535) {
      _setError('Port must be between 1 and 65535');
      return;
    }

    try {
      _setLoading(true);
      _statusMessage = 'Updating port configuration...';

      final newConfig = _config.copyWith(port: newPort);
      await _configService.saveConfig(newConfig);
      _config = newConfig;

      // Restart container if it's running to apply changes
      if (_config.isRunning) {
        _statusMessage = 'Restarting container to apply port changes...';
        await _dockerService.restartContainer();
        await Future.delayed(const Duration(seconds: 3));
        await _checkContainerStatus();
      }

      _statusMessage = 'Port updated successfully!';
      _clearError();
    } catch (e) {
      _setError('Failed to update port: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update localhost-only setting
  Future<void> updateLocalhostOnly(bool localhostOnly) async {
    if (_isLoading) return;

    try {
      _setLoading(true);
      _statusMessage = 'Updating network access settings...';

      final newConfig = _config.copyWith(localhostOnly: localhostOnly);
      await _configService.saveConfig(newConfig);
      _config = newConfig;

      // Restart container if it's running to apply changes
      if (_config.isRunning) {
        _statusMessage = 'Restarting container to apply network changes...';
        await _dockerService.restartContainer();
        await Future.delayed(const Duration(seconds: 3));
        await _checkContainerStatus();
      }

      _statusMessage = localhostOnly 
          ? 'Network access restricted to localhost only!'
          : 'Network access enabled for LAN devices!';
      _clearError();
    } catch (e) {
      _setError('Failed to update network access settings: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get container logs
  Future<String> getContainerLogs() async {
    try {
      return await _dockerService.getLogs();
    } catch (e) {
      return 'Error retrieving logs: $e';
    }
  }

  /// Refresh container status
  Future<void> refreshStatus() async {
    if (_isLoading) return;
    
    try {
      _setLoading(true);
      await _checkContainerStatus();
      _statusMessage = 'Status refreshed';
      _clearError();
    } catch (e) {
      _setError('Failed to refresh status: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _statusMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Get the web interface URL
  String get webInterfaceUrl => 'http://localhost:${_config.port}';
}
