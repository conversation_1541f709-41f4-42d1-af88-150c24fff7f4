import 'dart:async';
import 'package:multicast_dns/multicast_dns.dart';
import 'package:flutter/foundation.dart';

/// Service for discovering devices on the local network using mDNS
class NetworkDiscoveryService {
  static const String _serviceType = '_vaultwarden-desktop._tcp';

  MDnsClient? _mdnsClient;
  Timer? _discoveryTimer;
  final StreamController<Map<String, String>> _discoveredDevicesController = StreamController.broadcast();

  /// Stream of discovered devices (IP address -> device info)
  Stream<Map<String, String>> get discoveredDevices => _discoveredDevicesController.stream;

  /// Start mDNS service discovery
  Future<void> startDiscovery() async {
    try {
      _mdnsClient = MDnsClient();
      await _mdnsClient!.start();

      // Start periodic discovery
      _discoveryTimer = Timer.periodic(const Duration(seconds: 10), (_) {
        _performDiscovery();
      });

      // Perform initial discovery
      _performDiscovery();
      debugPrint('mDNS discovery started successfully');
    } catch (e) {
      debugPrint('Error starting mDNS discovery: $e');
      // Note: mDNS may not work in all network environments
      // This is expected behavior and not a critical error
    }
  }

  /// Stop discovery service
  Future<void> stopDiscovery() async {
    _discoveryTimer?.cancel();
    _discoveryTimer = null;

    try {
      _mdnsClient?.stop();
      _mdnsClient = null;
    } catch (e) {
      debugPrint('Error stopping mDNS discovery: $e');
    }
  }



  /// Perform a single discovery scan
  Future<void> _performDiscovery() async {
    if (_mdnsClient == null) return;

    try {
      // Look for Vaultwarden desktop services
      await for (final PtrResourceRecord ptr in _mdnsClient!
          .lookup<PtrResourceRecord>(ResourceRecordQuery.serverPointer(_serviceType))) {
        
        // Get service details
        await for (final SrvResourceRecord srv in _mdnsClient!
            .lookup<SrvResourceRecord>(ResourceRecordQuery.service(ptr.domainName))) {
          
          // Get additional info (TXT records)
          final txtRecords = <String, String>{};
          await for (final TxtResourceRecord txt in _mdnsClient!
              .lookup<TxtResourceRecord>(ResourceRecordQuery.text(ptr.domainName))) {
            
            // TXT records are stored as String, split by key=value format
            final recordString = txt.text;
            final parts = recordString.split('=');
            if (parts.length == 2) {
              txtRecords[parts[0]] = parts[1];
            }
          }

          // Get IP address
          await for (final IPAddressResourceRecord ip in _mdnsClient!
              .lookup<IPAddressResourceRecord>(ResourceRecordQuery.addressIPv4(srv.target))) {
            
            final deviceInfo = {
              'name': ptr.domainName,
              'ip': ip.address.address,
              'port': srv.port.toString(),
              'target': srv.target,
              ...txtRecords,
            };
            
            _discoveredDevicesController.add({ip.address.address: deviceInfo.toString()});
          }
        }
      }
    } catch (e) {
      debugPrint('Error during discovery: $e');
    }
  }

  /// Advertise this desktop as a Vaultwarden service
  Future<void> advertiseService({
    required int sshPort,
    required String publicKeyFingerprint,
    String? deviceName,
  }) async {
    if (_mdnsClient == null) {
      await startDiscovery();
    }

    try {
      final hostName = deviceName ?? 'VaAulLT-Desktop';

      // Create service advertisement info
      debugPrint('Advertising Vaultwarden desktop service on port $sshPort');
      debugPrint('Host: $hostName, Fingerprint: $publicKeyFingerprint');

      // Note: Actual mDNS service registration would require platform-specific implementation
      // For now, we just log the advertisement details
      // In a full implementation, you would register the service with mDNS here
    } catch (e) {
      debugPrint('Error advertising service: $e');
    }
  }

  /// Dispose of resources
  void dispose() {
    stopDiscovery();
    _discoveredDevicesController.close();
  }
}
