import '../services/logging_service.dart';

/// Custom exception types for better error categorization
abstract class VaAulLTException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
  final StackTrace? stackTrace;

  const VaAulLTException(
    this.message, {
    this.code,
    this.originalError,
    this.stackTrace,
  });

  @override
  String toString() => 'VaAulLTException: $message';
}

/// Network-related exceptions
class NetworkException extends VaAulLTException {
  const NetworkException(
    super.message, {
    super.code,
    super.originalError,
    super.stackTrace,
  });

  @override
  String toString() => 'NetworkException: $message';
}

/// Docker-related exceptions
class DockerException extends VaAulLTException {
  const DockerException(
    super.message, {
    super.code,
    super.originalError,
    super.stackTrace,
  });

  @override
  String toString() => 'DockerException: $message';
}

/// SSH-related exceptions
class SSHException extends VaAulLTException {
  const SSHException(
    super.message, {
    super.code,
    super.originalError,
    super.stackTrace,
  });

  @override
  String toString() => 'SSHException: $message';
}

/// Configuration-related exceptions
class ConfigurationException extends VaAulLTException {
  const ConfigurationException(
    super.message, {
    super.code,
    super.originalError,
    super.stackTrace,
  });

  @override
  String toString() => 'ConfigurationException: $message';
}

/// Service initialization exceptions
class ServiceException extends VaAulLTException {
  const ServiceException(
    super.message, {
    super.code,
    super.originalError,
    super.stackTrace,
  });

  @override
  String toString() => 'ServiceException: $message';
}

/// Centralized error handler for the application
class ErrorHandler {
  /// Handle and categorize errors appropriately
  static VaAulLTException handleError(
    dynamic error, {
    String? context,
    StackTrace? stackTrace,
  }) {
    final trace = stackTrace ?? StackTrace.current;
    final contextMsg = context != null ? '$context: ' : '';

    // Categorize based on error type and content
    if (error is VaAulLTException) {
      return error;
    }

    final errorString = error.toString().toLowerCase();

    // Network-related errors
    if (errorString.contains('socket') ||
        errorString.contains('connection') ||
        errorString.contains('network') ||
        errorString.contains('dns') ||
        errorString.contains('timeout') ||
        errorString.contains('unreachable')) {
      return NetworkException(
        '${contextMsg}Network error: $error',
        originalError: error,
        stackTrace: trace,
      );
    }

    // Docker-related errors
    if (errorString.contains('docker') ||
        errorString.contains('container') ||
        errorString.contains('compose')) {
      return DockerException(
        '${contextMsg}Docker error: $error',
        originalError: error,
        stackTrace: trace,
      );
    }

    // SSH-related errors
    if (errorString.contains('ssh') ||
        errorString.contains('authentication') ||
        errorString.contains('key') ||
        errorString.contains('tunnel')) {
      return SSHException(
        '${contextMsg}SSH error: $error',
        originalError: error,
        stackTrace: trace,
      );
    }

    // Configuration-related errors
    if (errorString.contains('config') ||
        errorString.contains('yaml') ||
        errorString.contains('parse') ||
        errorString.contains('invalid')) {
      return ConfigurationException(
        '${contextMsg}Configuration error: $error',
        originalError: error,
        stackTrace: trace,
      );
    }

    // Default to service exception
    return ServiceException(
      '${contextMsg}Service error: $error',
      originalError: error,
      stackTrace: trace,
    );
  }

  /// Log and handle error with appropriate level
  static void logAndHandle(
    dynamic error, {
    String? context,
    StackTrace? stackTrace,
    bool shouldRethrow = false,
  }) {
    final handledException = handleError(error, context: context, stackTrace: stackTrace);

    // Log based on exception type
    if (handledException is NetworkException) {
      LoggingService.network(
        handledException.message,
        error: handledException.originalError,
        stackTrace: handledException.stackTrace,
      );
    } else if (handledException is DockerException) {
      LoggingService.docker(
        handledException.message,
        error: handledException.originalError,
        stackTrace: handledException.stackTrace,
      );
    } else if (handledException is SSHException) {
      LoggingService.ssh(
        handledException.message,
        error: handledException.originalError,
        stackTrace: handledException.stackTrace,
      );
    } else if (handledException is ConfigurationException) {
      LoggingService.config(
        handledException.message,
        error: handledException.originalError,
        stackTrace: handledException.stackTrace,
      );
    } else {
      LoggingService.error(
        handledException.message,
        error: handledException.originalError,
        stackTrace: handledException.stackTrace,
      );
    }

    if (shouldRethrow) {
      throw handledException;
    }
  }

  /// Get user-friendly error message
  static String getUserFriendlyMessage(dynamic error) {
    if (error is NetworkException) {
      return 'Network connection issue. Please check your internet connection and try again.';
    } else if (error is DockerException) {
      return 'Docker service issue. Please ensure Docker is running and try again.';
    } else if (error is SSHException) {
      return 'SSH connection issue. Please check device connectivity and authentication.';
    } else if (error is ConfigurationException) {
      return 'Configuration error. Please check your settings and try again.';
    } else if (error is VaAulLTException) {
      return 'An unexpected error occurred. Please try again.';
    }

    // Fallback for unknown errors
    return 'An unexpected error occurred. Please try again.';
  }

  /// Check if error is recoverable
  static bool isRecoverable(dynamic error) {
    if (error is NetworkException || error is SSHException) {
      return true; // These can often be retried
    } else if (error is DockerException || error is ConfigurationException) {
      return false; // Usually requires user intervention
    }
    return false;
  }
}

/// Mixin to add error handling capabilities to classes
mixin ErrorHandlerMixin {
  /// Handle error with context
  VaAulLTException handleError(dynamic error, {String? context, StackTrace? stackTrace}) {
    return ErrorHandler.handleError(
      error,
      context: context ?? runtimeType.toString(),
      stackTrace: stackTrace,
    );
  }

  /// Log and handle error
  void logAndHandleError(
    dynamic error, {
    String? context,
    StackTrace? stackTrace,
    bool shouldRethrow = false,
  }) {
    ErrorHandler.logAndHandle(
      error,
      context: context ?? runtimeType.toString(),
      stackTrace: stackTrace,
      shouldRethrow: shouldRethrow,
    );
  }

  /// Get user-friendly error message
  String getUserFriendlyMessage(dynamic error) {
    return ErrorHandler.getUserFriendlyMessage(error);
  }
}
